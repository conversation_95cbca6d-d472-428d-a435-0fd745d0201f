import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time
import random

test_data = {
    "website_url": "https://the-internet.herokuapp.com/login",
    "username": "tomsmith",
    "password": "SuperSecretPassword!"
}

@pytest.mark.order(1)
def test_tc_001_successful_login(browser):
    try:
        login_page_url = test_data["website_url"]
        browser.get(login_page_url)
        WebDriverWait(browser, 10).until(EC.url_to_be(login_page_url))
        time.sleep(random.uniform(0.5, 1.5))
    except Exception as e:
        print(f"Exception in navigation: {repr(e)}")
        raise

    try:
        username = test_data["username"]
        locator = (By.ID, "username")
        wait = WebDriverWait(browser, 10)
        username_field = wait.until(EC.visibility_of_element_located(locator))
        username_field.click()
        username_field.clear()
        username_field.send_keys(username)
        time.sleep(random.uniform(0.5, 1.5))
        WebDriverWait(browser, 5).until(EC.text_to_be_present_in_element_value(locator, username))
        assert username_field.get_attribute("value") == username, "Username field does not contain the expected text."
    except Exception as e:
        print(f"Exception in username input: {repr(e)}")
        raise

    try:
        password = test_data["password"]
        locator = (By.ID, "password")
        wait = WebDriverWait(browser, 10)
        password_field = wait.until(EC.visibility_of_element_located(locator))
        password_field.click()
        password_field.clear()
        password_field.send_keys(password)
        time.sleep(random.uniform(0.5, 1.5))
        WebDriverWait(browser, 5).until(EC.text_to_be_present_in_element_value(locator, password))
        assert password_field.get_attribute("value") == password, "Password field does not contain the expected text."
    except Exception as e:
        print(f"Exception in password input: {repr(e)}")
        raise

    try:
        locator = (By.CSS_SELECTOR, "button.radius")
        wait = WebDriverWait(browser, 10)
        login_button = wait.until(EC.element_to_be_clickable(locator))
        login_button.click()
        time.sleep(random.uniform(0.5, 1.5))
        assert EC.url_contains("secure")(browser)
    except Exception as e:
        print(f"Exception in login attempt: {repr(e)}")
        raise