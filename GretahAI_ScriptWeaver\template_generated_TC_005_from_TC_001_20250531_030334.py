import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time
import random

test_data = {
    "website_url": "https://the-internet.herokuapp.com/login",
    "username": "tomsmith",
    "password": "SuperSecretPassword!",
    "incorrect_password_1": "wrong_password_1",
    "incorrect_password_2": "wrong_password_2"
}

def test_tc_005_successful_login_after_two_failed_attempts(browser):
    # Step 1: Navigate to the login page
    try:
        browser.get(test_data["website_url"])
        WebDriverWait(browser, 10).until(EC.url_to_be(test_data["website_url"]))
        time.sleep(random.uniform(0.5, 1.5))
    except Exception as e:
        print(repr(e))
        raise

    # Step 2: Attempt login with incorrect password
    try:
        username_locator = (By.ID, "username")
        password_locator = (By.ID, "password")
        login_button_locator = (By.CSS_SELECTOR, "button.radius")
        message_locator = (By.ID, "flash")

        username_field = WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located(username_locator)
        )
        password_field = WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located(password_locator)
        )
        username_field.clear()
        username_field.send_keys(test_data["username"])
        password_field.clear()
        password_field.send_keys(test_data["incorrect_password_1"])

        login_button = WebDriverWait(browser, 10).until(
            EC.element_to_be_clickable(login_button_locator)
        )
        login_button.click()
        time.sleep(random.uniform(0.5, 1.5))

        WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located(message_locator)
        )

        error_message = browser.find_element(*message_locator).text
        assert "Your username is invalid!" in error_message or "Your password is invalid!" in error_message, "Error message not found after incorrect login attempt 1"

    except Exception as e:
        print(repr(e))
        raise

    # Step 3: Attempt login with incorrect password again
    try:
        username_locator = (By.ID, "username")
        password_locator = (By.ID, "password")
        login_button_locator = (By.CSS_SELECTOR, "button.radius")
        message_locator = (By.ID, "flash")

        username_field = WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located(username_locator)
        )
        password_field = WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located(password_locator)
        )
        username_field.clear()
        username_field.send_keys(test_data["username"])
        password_field.clear()
        password_field.send_keys(test_data["incorrect_password_2"])

        login_button = WebDriverWait(browser, 10).until(
            EC.element_to_be_clickable(login_button_locator)
        )
        login_button.click()
        time.sleep(random.uniform(0.5, 1.5))

        WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located(message_locator)
        )
        error_message = browser.find_element(*message_locator).text
        assert "Your username is invalid!" in error_message or "Your password is invalid!" in error_message, "Error message not found after incorrect login attempt 2"

    except Exception as e:
        print(repr(e))
        raise

    # Step 4: Attempt login with correct credentials and verify successful login
    try:
        username_locator = (By.ID, "username")
        password_locator = (By.ID, "password")
        login_button_locator = (By.CSS_SELECTOR, "button.radius")
        success_message_locator = (By.ID, "flash")
        username_field = WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located(username_locator)
        )
        password_field = WebDriverWait(browser, 10).until(
            EC.visibility_of_element_located(password_locator)
        )
        username_field.clear()
        username_field.send_keys(test_data["username"])
        password_field.clear()
        password_field.send_keys(test_data["password"])

        login_button = WebDriverWait(browser, 10).until(
            EC.element_to_be_clickable(login_button_locator)
        )
        login_button.click()
        time.sleep(random.uniform(0.5, 1.5))

        WebDriverWait(browser, 10).until(
            EC.url_contains("secure")
        )

        success_message = browser.find_element(*success_message_locator).text
        assert "You logged into a secure area!" in success_message, "Success message not found after correct login"
        assert "secure" in browser.current_url, "URL should contain 'secure' after login"
    except Exception as e:
        print(repr(e))
        raise