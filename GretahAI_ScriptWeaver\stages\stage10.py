"""
Stage 10: Script Playground for GretahAI ScriptWeaver

This module provides an independent script experimentation and generation utility.
It provides functionality for:
- Loading optimized scripts as templates
- Selecting target test cases for generation
- Template-based AI script generation using proven patterns
- Managing generated scripts with proper metadata
- Independent access regardless of workflow stage

Key Features:
- **Always Accessible**: Can be accessed at any time, regardless of workflow stage
- **Template-Based Generation**: Uses optimized scripts as templates for new test cases
- **AI-Powered Adaptation**: Leverages Google AI to adapt templates to new requirements
- **Quality Preservation**: Maintains optimization patterns and best practices from templates
- **Playground Nature**: Functions as an experimental environment for script generation

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Modular AI integration via core.ai_template module

Functions:
    stage10_script_playground(state): Main Stage 10 function for script playground
"""

import os
import logging
import streamlit as st
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

# Import core dependencies
from state_manager import StateStage
from debug_utils import debug

# Import helper functions
from core.template_helpers import (
    get_optimized_scripts_for_templates,
    format_template_script_display,
    get_available_test_cases,
    format_test_case_display,
    validate_template_generation_inputs,
    create_template_generation_filename
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10")

# Constants for Stage 10
RECENT_SCRIPTS_TIME_LIMIT_HOURS = 1
MAX_RECENT_SCRIPTS_DISPLAY = 3
STAGE10_EXECUTION_TIMEOUT = 300  # 5 minutes


def _validate_stage10_prerequisites(state) -> tuple[bool, str]:
    """
    Validate prerequisites for Stage 10 operations.

    Args:
        state: StateManager instance

    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Check for Google AI API key
        if not hasattr(state, 'google_api_key') or not state.google_api_key:
            return False, "Google AI API key is required for script generation"

        # Check script storage initialization
        if not hasattr(state, '_script_storage') or state._script_storage is None:
            return False, "Script storage not initialized"

        return True, ""

    except Exception as e:
        debug(f"Stage 10 prerequisite validation error: {e}")
        return False, f"Validation error: {e}"


def _get_cached_recent_scripts(state) -> List[Dict[str, Any]]:
    """
    Get recently generated scripts with caching for performance.

    Args:
        state: StateManager instance

    Returns:
        List of recent template-generated scripts
    """
    try:
        # Check if we have cached results that are still fresh
        cache_key = '_stage10_recent_scripts_cache'
        cache_timestamp_key = '_stage10_cache_timestamp'

        if (hasattr(state, cache_key) and hasattr(state, cache_timestamp_key) and
            getattr(state, cache_timestamp_key) and
            (datetime.now() - getattr(state, cache_timestamp_key)).seconds < 30):  # 30 second cache
            debug("Stage 10: Using cached recent scripts")
            return getattr(state, cache_key)

        # Get fresh data
        if not hasattr(state, '_script_storage') or not state._script_storage:
            return []

        all_scripts = state._script_storage.get_all_scripts()
        time_limit = datetime.now() - timedelta(hours=RECENT_SCRIPTS_TIME_LIMIT_HOURS)

        recent_scripts = []
        for script in all_scripts:
            if (script.get('type') == 'template_generated' and
                script.get('metadata', {}).get('template_based', False)):

                timestamp_str = script.get('metadata', {}).get('generation_timestamp')
                if timestamp_str:
                    try:
                        script_time = datetime.fromisoformat(timestamp_str)
                        if script_time > time_limit:
                            recent_scripts.append(script)
                    except Exception:
                        continue

        # Sort by generation time (most recent first)
        recent_scripts.sort(
            key=lambda x: x.get('metadata', {}).get('generation_timestamp', ''),
            reverse=True
        )

        # Cache the results
        setattr(state, cache_key, recent_scripts)
        setattr(state, cache_timestamp_key, datetime.now())

        debug(f"Stage 10: Found {len(recent_scripts)} recent scripts")
        return recent_scripts

    except Exception as e:
        debug(f"Error getting recent scripts: {e}")
        return []


def stage10_script_playground(state):
    """
    Stage 10: Script Playground.

    This stage provides an independent, always-accessible script experimentation and generation utility.
    It loads optimized scripts as templates and generates new scripts for different test cases,
    preserving the optimization patterns and best practices from the templates.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
    st.markdown("*Experiment with script generation using optimized scripts as templates*")

    debug("Stage 10: Script Playground accessed")
    debug(f"Stage 10: State object ID: {id(state)}")
    debug(f"Stage 10: Session state object ID: {id(st.session_state.get('state', None))}")

    # Validate prerequisites first
    is_valid, error_msg = _validate_stage10_prerequisites(state)
    if not is_valid:
        st.warning(f"⚠️ **Prerequisites Missing**: {error_msg}")
        if "API key" in error_msg:
            st.info("Please configure your Google AI API key in Stage 2 (Website Configuration) first.")
            if st.button("🔧 Go to Stage 2", type="primary", key="stage10_goto_stage2"):
                state.advance_to(StateStage.STAGE2_WEBSITE, "User navigated to Stage 2 from Stage 10 for API key setup")
                st.rerun()
                return
        return

    # Check for pending script executions first (this handles button click reruns)
    _check_and_execute_pending_scripts(state)

    # Display any existing execution results at the top (persistent across reruns)
    if hasattr(state, 'stage10_execution_results') and state.stage10_execution_results:
        st.markdown("---")
        st.markdown("### 📊 Recent Script Execution Results")
        _display_stage10_execution_results(state)
        st.markdown("---")

    # Initialize script storage if needed
    if not hasattr(state, '_script_storage') or state._script_storage is None:
        state._init_script_storage()

    # Load optimized scripts for templates with error handling
    try:
        optimized_scripts = get_optimized_scripts_for_templates(state._script_storage)
        available_test_cases = get_available_test_cases(state)
    except Exception as e:
        st.error(f"❌ **Data Loading Error**: {e}")
        debug(f"Stage 10 data loading error: {e}")
        return

    # Check if we have the necessary data
    if not optimized_scripts:
        st.info("🎮 **Script Playground is Empty**")
        st.markdown("Create optimized scripts first to use as templates.")

        # Provide navigation to create templates
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📁 Start New Project", use_container_width=True, type="primary", key="stage10_start_new_project"):
                debug("User navigated to Stage 1 from Script Playground - no templates")
                state.advance_to(StateStage.STAGE1_UPLOAD, "User started new project from Script Playground")
                st.rerun()
                return
        with col2:
            if st.button("⚙️ Generate Scripts", use_container_width=True, key="stage10_generate_scripts"):
                debug("User navigated to Stage 6 from Script Playground - no templates")
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to script generation from Script Playground")
                st.rerun()
                return
        with col3:
            if st.button("🔧 Optimize Scripts", use_container_width=True, key="stage10_optimize_scripts"):
                debug("User navigated to Stage 8 from Script Playground - no templates")
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to optimization from Script Playground")
                st.rerun()
                return
        return

    if not available_test_cases:
        st.warning("⚠️ **No Test Cases Available**")
        st.markdown("Upload a CSV file with test cases to generate scripts.")

        if st.button("📁 Upload Test Cases", use_container_width=True, type="primary", key="stage10_upload_test_cases"):
            debug("User navigated to Stage 1 from Script Playground - no test cases")
            state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to upload from Script Playground")
            st.rerun()
            return
        return

    # Template Selection Section
    with st.expander("🎯 Template Selection", expanded=True):
        st.markdown("**Select an optimized script to use as a template:**")

        # Create template options
        template_options = []
        template_map = {}

        for script in optimized_scripts:
            display_info = format_template_script_display(script)
            option_text = f"{display_info['title']} - {display_info['timestamp']}"
            template_options.append(option_text)
            template_map[option_text] = script

        if template_options:
            selected_template_option = st.selectbox(
                "Available Templates",
                template_options,
                key="template_selection"
            )

            selected_template = template_map[selected_template_option]

            # Display template details
            col1, col2 = st.columns(2)
            with col1:
                st.info(f"""
                **Template Details:**
                - Test Case: {selected_template.get('test_case_id', 'Unknown')}
                - Created: {format_template_script_display(selected_template)['timestamp']}
                - Size: {format_template_script_display(selected_template)['size_info']}
                """)

            with col2:
                st.info(f"""
                **Optimization Info:**
                - Status: ✅ Optimized
                - Type: {selected_template.get('type', 'Unknown').title()}
                - {format_template_script_display(selected_template)['optimization_info']}
                """)

            # Template preview toggle
            if st.checkbox("📄 Show Template Preview", key="show_template_preview"):
                st.markdown("**Template Code:**")
                template_content = selected_template.get('content', 'No content available')
                st.code(template_content, language='python')

    # Test Case Selection Section
    with st.expander("📋 Target Test Case Selection", expanded=True):
        st.markdown("**Select a test case to generate a script for:**")

        # Create test case options
        test_case_options = []
        test_case_map = {}

        for test_case in available_test_cases:
            option_text = format_test_case_display(test_case)
            test_case_options.append(option_text)
            test_case_map[option_text] = test_case

        if test_case_options:
            selected_test_case_option = st.selectbox(
                "Available Test Cases",
                test_case_options,
                key="test_case_selection"
            )

            selected_test_case = test_case_map[selected_test_case_option]

            # Display test case details
            tc_id = selected_test_case.get('Test Case ID', 'Unknown')
            tc_objective = selected_test_case.get('Test Case Objective', 'No objective specified')
            tc_steps = selected_test_case.get('Steps', [])

            st.info(f"""
            **Target Test Case:**
            - ID: {tc_id}
            - Objective: {tc_objective}
            - Steps: {len(tc_steps)} steps defined
            """)

    # Template-Based Generation Section
    with st.expander("🤖 Template-Based Generation", expanded=True):
        st.markdown("**Generate a new script using the selected template and test case:**")

        # Validate inputs
        if 'selected_template' in locals() and 'selected_test_case' in locals():
            is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)

            if not is_valid:
                st.error(f"❌ **Validation Error**: {error_message}")
                return

            # Generation controls
            col1, col2 = st.columns(2)

            with col1:
                custom_instructions = st.text_area(
                    "Custom Instructions (Optional)",
                    placeholder="Add any specific requirements or modifications...",
                    key="template_custom_instructions"
                )

            with col2:
                st.markdown("**Generation Settings:**")
                preserve_structure = st.checkbox("Preserve Template Structure", value=True, key="preserve_structure")
                include_error_handling = st.checkbox("Include Error Handling", value=True, key="include_error_handling")

            # Generate button
            if st.button("🚀 Generate Script from Template", use_container_width=True, type="primary", key="stage10_generate_from_template"):
                _generate_script_from_template(
                    state, selected_template, selected_test_case,
                    custom_instructions, preserve_structure, include_error_handling
                )
        else:
            st.info("👆 Please select both a template and target test case above to enable generation.")

    # Persistent Script Execution Section (shows recently generated scripts)
    _display_persistent_execution_section(state)

    # Optional workflow navigation (minimalist)
    with st.expander("🧭 Quick Navigation", expanded=False):
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📁 Upload CSV (Stage 1)", use_container_width=True, key="stage10_nav_upload_csv"):
                debug("User navigated to Stage 1 from Script Playground")
                state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to Stage 1 from Script Playground")
                st.rerun()
                return

        with col2:
            if st.button("🔧 Optimize Scripts (Stage 8)", use_container_width=True, key="stage10_nav_optimize_scripts"):
                debug("User navigated to Stage 8 from Script Playground")
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to Stage 8 from Script Playground")
                st.rerun()
                return

        with col3:
            if st.button("📜 Script Browser (Stage 9)", use_container_width=True, key="stage10_nav_script_browser"):
                debug("User navigated to Stage 9 from Script Playground")
                state.advance_to(StateStage.STAGE9_BROWSE, "User navigated to Stage 9 from Script Playground")
                st.rerun()
                return


def _generate_script_from_template(state, template_script, target_test_case,
                                 custom_instructions, preserve_structure, include_error_handling):
    """
    Generate a new script using the selected template and test case.

    Args:
        state: StateManager instance
        template_script: Selected template script
        target_test_case: Target test case for generation
        custom_instructions: User's custom instructions
        preserve_structure: Whether to preserve template structure
        include_error_handling: Whether to include error handling
    """
    try:
        debug("Starting template-based script generation")

        # Import AI function inside the function to avoid circular imports
        from core.ai_template import generate_template_based_script

        # Validate inputs before AI call
        is_valid, validation_error = validate_template_generation_inputs(template_script, target_test_case)
        if not is_valid:
            st.error(f"❌ **Validation Error**: {validation_error}")
            debug(f"Template generation validation failed: {validation_error}")
            return

        with st.spinner("🤖 Generating script from template..."):
            # Generate script using the modular AI function
            debug("Calling modular AI function for template-based script generation")

            # Track generation time for performance monitoring
            start_time = datetime.now()

            generated_script = generate_template_based_script(
                template_script=template_script,
                target_test_case=target_test_case,
                custom_instructions=custom_instructions,
                preserve_structure=preserve_structure,
                include_error_handling=include_error_handling,
                website_url=getattr(state, 'website_url', None),
                api_key=getattr(state, 'google_api_key', None),
                model_name="gemini-2.0-flash"
            )

            # Log generation time
            generation_time = (datetime.now() - start_time).total_seconds()
            debug(f"Template-based script generation completed in {generation_time:.2f} seconds")

            if generated_script and generated_script.strip():
                _handle_successful_generation(state, template_script, target_test_case, generated_script)
            else:
                st.error("❌ Failed to generate script. Please try again.")
                debug("Template-based script generation failed - no script returned")

    except ImportError as e:
        error_msg = f"AI module import failed: {e}"
        st.error(f"❌ **Import Error**: {error_msg}")
        debug(f"Template-based script generation import error: {e}")
    except Exception as e:
        error_msg = f"Template-based script generation failed: {e}"
        st.error(f"❌ **Generation Error**: {error_msg}")
        debug(f"Template-based script generation error: {e}")



def _handle_successful_generation(state, template_script, target_test_case, generated_script):
    """
    Handle successful script generation with display and storage.

    Args:
        state: StateManager instance
        template_script: Template script used
        target_test_case: Target test case
        generated_script: Generated script content
    """
    try:
        debug("Handling successful template-based script generation")

        # Create filename
        filename = create_template_generation_filename(template_script, target_test_case)

        # Display success message
        st.success("✅ **Script Generated Successfully!**")

        # Display generated script
        st.markdown("### 📄 Generated Script")
        st.code(generated_script, language='python')

        # Download and copy buttons
        col1, col2 = st.columns(2)
        with col1:
            st.download_button(
                label="📥 Download Script",
                data=generated_script,
                file_name=filename,
                mime="text/x-python",
                use_container_width=True
            )

        with col2:
            if st.button("📋 Copy to Clipboard", use_container_width=True, key="stage10_copy_to_clipboard"):
                st.code(generated_script)  # This allows easy copying
                st.info("Script displayed above for copying")

        # Note: Script execution is now available in the persistent execution section below

        # Save to script storage
        template_metadata = {
            'generation_type': 'template_based',
            'template_script_id': template_script.get('id'),
            'template_test_case_id': template_script.get('test_case_id'),
            'target_test_case_id': target_test_case.get('Test Case ID'),
            'generation_timestamp': datetime.now().isoformat(),
            'template_based': True,
            'optimization_status': 'template_generated'
        }

        state.add_script_to_history(
            script_content=generated_script,
            script_type='template_generated',
            step_no=None,
            file_path=filename,
            metadata=template_metadata
        )

        debug(f"Template-based script saved with filename: {filename}")

        # Display generation summary
        st.info(f"""
        **Generation Summary:**
        - Template: {template_script.get('test_case_id', 'Unknown')}
        - Target: {target_test_case.get('Test Case ID', 'Unknown')}
        - Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        - Filename: {filename}
        """)

    except Exception as e:
        error_msg = f"Failed to handle successful generation: {e}"
        st.error(f"❌ **Storage Error**: {error_msg}")
        debug(f"Error handling successful generation: {e}")


def _display_persistent_execution_section(state):
    """
    Display a persistent section for executing recently generated scripts.
    This section is always visible and allows users to execute scripts even after page reruns.

    Args:
        state (StateManager): The application state manager instance
    """
    # Use cached function for better performance
    recent_scripts = _get_cached_recent_scripts(state)

    if not recent_scripts:
        return

    # Display the execution section
    st.markdown("---")
    with st.expander("🚀 Execute Recent Scripts", expanded=True):
        st.markdown("**Recently generated scripts available for execution:**")

        # Show up to MAX_RECENT_SCRIPTS_DISPLAY most recent scripts
        display_scripts = recent_scripts[:MAX_RECENT_SCRIPTS_DISPLAY]

        for i, script in enumerate(display_scripts):
            script_id = script.get('id', f'script_{i}')
            metadata = script.get('metadata', {})
            target_test_case_id = metadata.get('target_test_case_id', 'Unknown')
            template_test_case_id = metadata.get('template_test_case_id', 'Unknown')
            generation_time = metadata.get('generation_timestamp', '')

            # Format timestamp with error handling
            formatted_time = _format_timestamp_safe(generation_time)

            # Create filename for execution with safe fallback
            filename = script.get('file_path') or _create_safe_filename(target_test_case_id, formatted_time)

            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                st.markdown(f"""
                **Script {i+1}:**
                - Target: {target_test_case_id}
                - Template: {template_test_case_id}
                - Generated: {formatted_time}
                """)

            with col2:
                # Show script preview button with proper styling
                if st.button(f"👁️ Preview", key=f"preview_{script_id}", help="Preview script content"):
                    st.code(script.get('content', 'No content available'), language='python')

            with col3:
                # Execute script button with proper styling
                if st.button(f"▶️ Execute", key=f"execute_{script_id}", type="secondary",
                           help="Execute this script with pytest"):
                    _handle_script_execution_request(state, script, filename, target_test_case_id)

            if i < len(display_scripts) - 1:
                st.markdown("---")


def _format_timestamp_safe(timestamp_str: str) -> str:
    """Safely format timestamp string with fallback."""
    try:
        if timestamp_str:
            dt = datetime.fromisoformat(timestamp_str)
            return dt.strftime('%H:%M:%S')
        return 'Unknown'
    except Exception:
        return 'Unknown'


def _create_safe_filename(target_test_case_id: str, formatted_time: str) -> str:
    """Create a safe filename with proper sanitization."""
    safe_id = target_test_case_id.replace(' ', '_').replace('/', '_')
    safe_time = formatted_time.replace(':', '')
    return f"template_generated_{safe_id}_{safe_time}.py"


def _handle_script_execution_request(state, script: Dict[str, Any], filename: str, target_test_case_id: str):
    """Handle script execution request with proper state management."""
    debug(f"Stage 10: Execute button clicked for script {script.get('id', 'unknown')}")

    # Create target test case data for execution
    target_test_case = {
        'Test Case ID': target_test_case_id,
        'Test Case Objective': f'Execute template-generated script for {target_test_case_id}'
    }

    # Initialize execution pending dict if needed
    if not hasattr(state, 'stage10_execution_pending'):
        state.stage10_execution_pending = {}
        debug("Stage 10: Created stage10_execution_pending attribute")

    # Add to pending executions
    state.stage10_execution_pending[filename] = {
        'script_filename': filename,
        'script_content': script.get('content', ''),
        'target_test_case': target_test_case,
        'timestamp': datetime.now().isoformat()
    }

    debug(f"Stage 10: Added pending execution for {filename}")
    debug(f"Stage 10: Pending executions now: {list(state.stage10_execution_pending.keys())}")

    # Update session state and trigger rerun to start execution
    st.session_state['state'] = state
    debug("Stage 10: Updated session state, triggering rerun")
    st.rerun()


def _check_and_execute_pending_scripts(state):
    """
    Check for pending script executions and execute them.
    This function is called after the button click rerun to actually execute the script.

    Args:
        state (StateManager): The application state manager instance
    """
    debug(f"Stage 10: Checking for pending executions...")
    debug(f"Stage 10: Has pending attribute: {hasattr(state, 'stage10_execution_pending')}")

    if hasattr(state, 'stage10_execution_pending'):
        debug(f"Stage 10: Pending executions count: {len(state.stage10_execution_pending)}")
        debug(f"Stage 10: Pending executions: {list(state.stage10_execution_pending.keys())}")

    if not hasattr(state, 'stage10_execution_pending') or not state.stage10_execution_pending:
        debug("Stage 10: No pending executions found")
        return

    # Get the first pending execution
    for script_filename, execution_data in list(state.stage10_execution_pending.items()):
        debug(f"Stage 10: Found pending execution for {script_filename}")

        # Remove from pending list
        del state.stage10_execution_pending[script_filename]
        debug(f"Stage 10: Removed {script_filename} from pending list")

        # Execute the script
        _execute_generated_script(
            state,
            execution_data['script_filename'],
            execution_data['script_content'],
            execution_data['target_test_case']
        )

        # Only execute one script at a time
        break


def _execute_generated_script(state, script_filename, script_content, target_test_case):
    """
    Execute the generated script using pytest with the same methodology as Stage 7.

    Args:
        state (StateManager): The application state manager instance
        script_filename (str): Name of the script file
        script_content (str): Content of the generated script
        target_test_case (dict): Target test case information
    """
    import os
    import subprocess
    from datetime import datetime
    from pathlib import Path

    debug("Stage 10: Starting script execution")
    execution_start_time = datetime.now()

    try:
        # Ensure the script file exists and is written correctly
        script_path = Path(script_filename)

        # Always write the script content to ensure it's up to date
        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            debug(f"Stage 10: Script file written successfully: {script_path}")

            # Verify the file was written correctly
            if not script_path.exists():
                raise FileNotFoundError(f"Script file was not created: {script_path}")

            # Check file size
            file_size = script_path.stat().st_size
            if file_size == 0:
                raise ValueError(f"Script file is empty: {script_path}")

            debug(f"Stage 10: Script file verified - size: {file_size} bytes")

        except Exception as file_error:
            error_msg = f"Failed to write script file: {file_error}"
            st.error(f"❌ **File Error**: {error_msg}")
            debug(f"Stage 10 file error: {file_error}")

            # Store error in state
            if not hasattr(state, 'stage10_execution_results'):
                state.stage10_execution_results = {}

            state.stage10_execution_results[script_filename] = {
                'script_filename': script_filename,
                'target_test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
                'execution_timestamp': datetime.now().isoformat(),
                'error': error_msg,
                'success': False,
                'error_type': 'file_creation_error'
            }
            return

        with st.spinner(f"Running generated script: {script_filename}..."):
            # Import the JUnit parser (reusing Stage 7 patterns)
            from core.junit_parser import parse_junit_xml, format_test_results_for_display

            # Set environment variables for the test run (same as Stage 7)
            env = os.environ.copy()
            env["HEADLESS"] = "0"  # Always run in visible mode
            env["PYTEST_QUIET_MODE"] = "1"  # Quiet mode for Stage 10

            # Generate timestamped result file name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_xml_path = f"stage10_results_{timestamp}.xml"

            # Quiet pytest command for Stage 10 (minimal console output)
            pytest_command = [
                "pytest",
                str(script_path),
                f"--junitxml={result_xml_path}",
                "--log-cli-level=WARNING",  # Only show warnings and errors
                "--capture=no",  # Capture output for cleaner console
                "--tb=short",  # Short traceback format
                "-q"  # Quiet mode
            ]

            debug(f"Stage 10: Executing pytest command: {' '.join(pytest_command)}")
            debug(f"Stage 10: Working directory: {os.getcwd()}")
            debug(f"Stage 10: Environment HEADLESS: {env.get('HEADLESS', 'not set')}")

            # Run the test script using enhanced pytest configuration
            result = subprocess.run(
                pytest_command,
                capture_output=True, text=True,
                env=env,
                cwd=os.getcwd()  # Ensure we're in the correct directory
            )

            # Store execution results in state for display
            execution_results = {
                'script_filename': script_filename,
                'target_test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
                'execution_timestamp': datetime.now().isoformat(),
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'xml_file': result_xml_path,
                'success': result.returncode == 0,
                'pytest_command': ' '.join(pytest_command)
            }

            # Parse XML results if available
            xml_results = None
            if Path(result_xml_path).exists():
                try:
                    xml_results = parse_junit_xml(result_xml_path)
                    if xml_results:
                        execution_results['xml_results'] = xml_results
                        execution_results['formatted_results'] = format_test_results_for_display(xml_results)
                        debug(f"Stage 10: Successfully parsed XML results from {result_xml_path}")
                    else:
                        debug(f"Stage 10: XML file exists but parsing returned None: {result_xml_path}")
                except Exception as xml_error:
                    debug(f"Stage 10: Failed to parse XML results: {xml_error}")
                    execution_results['xml_parse_error'] = str(xml_error)
            else:
                debug(f"Stage 10: XML results file not found: {result_xml_path}")

            # Store results in state (using a Stage 10 specific field)
            if not hasattr(state, 'stage10_execution_results'):
                state.stage10_execution_results = {}

            state.stage10_execution_results[script_filename] = execution_results

            # Calculate execution time for performance monitoring
            execution_time = (datetime.now() - execution_start_time).total_seconds()
            debug(f"Stage 10: Script execution completed with return code {result.returncode} in {execution_time:.2f} seconds")

            # Add execution time to results
            execution_results['execution_time_seconds'] = execution_time

            # Display immediate feedback with performance info
            if result.returncode == 0:
                st.success("✅ **Script executed successfully!**")
                if xml_results:
                    summary = xml_results.get('summary', {})
                    total_tests = summary.get('total_tests', 0)
                    passed_tests = summary.get('passed_tests', 0)
                    st.info(f"**Test Results:** {passed_tests}/{total_tests} tests passed in {execution_time:.2f}s")
                else:
                    st.info(f"**Test completed successfully** in {execution_time:.2f}s (detailed results parsing in progress)")
            else:
                st.error("❌ **Script execution failed!**")
                st.error(f"Exit code: {result.returncode} (execution time: {execution_time:.2f}s)")

                # Show brief error details immediately
                if result.stderr:
                    # Show only the last few lines of stderr for immediate feedback
                    stderr_lines = result.stderr.strip().split('\n')
                    if stderr_lines:
                        last_error_line = stderr_lines[-1] if stderr_lines else "Unknown error"
                        st.error(f"Error: {last_error_line}")

            # Update session state without triggering rerun
            st.session_state['state'] = state

            # Display the results immediately after execution (without clear button to avoid duplicates)
            st.markdown("---")
            _display_stage10_execution_results(state, show_clear_button=False)

    except Exception as e:
        import traceback
        error_msg = f"Failed to execute script: {e}"
        traceback_str = traceback.format_exc()

        st.error(f"❌ **Execution Error**: {error_msg}")
        debug(f"Stage 10 execution error: {e}")
        debug(f"Stage 10 execution traceback: {traceback_str}")

        # Show detailed error information
        with st.expander("🔍 Error Details", expanded=True):
            st.code(traceback_str, language="text")

        # Store error in state
        if not hasattr(state, 'stage10_execution_results'):
            state.stage10_execution_results = {}

        state.stage10_execution_results[script_filename] = {
            'script_filename': script_filename,
            'target_test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
            'execution_timestamp': datetime.now().isoformat(),
            'error': error_msg,
            'traceback': traceback_str,
            'success': False,
            'error_type': 'python_exception'
        }

        # Update session state without triggering rerun
        st.session_state['state'] = state

        # Display the error results immediately (without clear button to avoid duplicates)
        st.markdown("---")
        _display_stage10_execution_results(state, show_clear_button=False)


def _display_stage10_execution_results(state, show_clear_button=True):
    """
    Display execution results for Stage 10 script executions.

    Args:
        state (StateManager): The application state manager instance
        show_clear_button (bool): Whether to show the clear results button
    """
    if not hasattr(state, 'stage10_execution_results') or not state.stage10_execution_results:
        return

    # Get the most recent execution result
    latest_result = None
    latest_timestamp = None

    for result in state.stage10_execution_results.values():
        timestamp = result.get('execution_timestamp')
        if timestamp and (latest_timestamp is None or timestamp > latest_timestamp):
            latest_result = result
            latest_timestamp = timestamp

    if not latest_result:
        return

    st.markdown("### 📊 Execution Results")

    # Display execution status
    if latest_result.get('success', False):
        st.success("✅ **Last execution completed successfully**")
    else:
        st.error("❌ **Last execution failed**")

    # Create columns for result details
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "Script",
            latest_result.get('script_filename', 'Unknown')
        )

    with col2:
        st.metric(
            "Test Case",
            latest_result.get('target_test_case_id', 'Unknown')
        )

    with col3:
        execution_time = latest_result.get('execution_timestamp', '')
        if execution_time:
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(execution_time)
                formatted_time = dt.strftime('%H:%M:%S')
            except:
                formatted_time = execution_time
        else:
            formatted_time = 'Unknown'

        st.metric("Executed At", formatted_time)

    # Display detailed results if available
    if 'formatted_results' in latest_result:
        formatted_results = latest_result['formatted_results']

        with st.expander("📋 Detailed Test Results", expanded=False):
            # Display summary
            summary = formatted_results.get('summary', {})
            if summary:
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Total Tests", summary.get('total_tests', 0))
                with col2:
                    st.metric("Passed", summary.get('passed_tests', 0))
                with col3:
                    st.metric("Failed", summary.get('failed_tests', 0))
                with col4:
                    st.metric("Duration", f"{summary.get('total_time', 0):.2f}s")

            # Display individual test details
            test_details = formatted_results.get('test_details', [])
            if test_details:
                st.markdown("**Individual Test Results:**")
                for test in test_details:
                    status_icon = "✅" if test['status'] == 'passed' else "❌"
                    st.markdown(f"{status_icon} **{test['name']}** - {test['duration']}")

                    if test['status'] != 'passed' and test.get('message'):
                        st.error(f"Error: {test['message']}")

    # Display error details if execution failed
    elif 'error' in latest_result:
        with st.expander("❌ Error Details", expanded=True):
            st.error(latest_result['error'])

            # Show error type if available
            error_type = latest_result.get('error_type', 'unknown')
            st.info(f"**Error Type:** {error_type}")

            # Show traceback if available
            if 'traceback' in latest_result:
                st.markdown("**Full Traceback:**")
                st.code(latest_result['traceback'], language='text')

    # Display raw output if available (for debugging)
    elif 'stderr' in latest_result and latest_result['stderr']:
        with st.expander("🔍 Debug Output", expanded=False):
            st.code(latest_result['stderr'], language='text')

            # Also show stdout if available
            if 'stdout' in latest_result and latest_result['stdout']:
                st.markdown("**Standard Output:**")
                st.code(latest_result['stdout'], language='text')

            # Show pytest command for debugging
            if 'pytest_command' in latest_result:
                st.markdown("**Pytest Command:**")
                st.code(latest_result['pytest_command'], language='bash')

    # Clear results button (only show if requested)
    if show_clear_button:
        if st.button("🗑️ Clear Execution Results", use_container_width=True, key="stage10_clear_execution_results"):
            if hasattr(state, 'stage10_execution_results'):
                state.stage10_execution_results.clear()
                st.session_state['state'] = state
                st.rerun()
